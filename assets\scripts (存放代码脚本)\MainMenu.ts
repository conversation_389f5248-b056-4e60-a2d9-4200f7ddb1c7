import { _decorator, Component, director } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('MainMenu')
export class MainMenu extends Component {
    start() {
        // 组件脚本的 start 方法，在节点第一次激活时执行一次
        console.log('主菜单脚本启动');
    }

    /**
     * 当开始游戏按钮被点击时调用
     */
    onStartGameClicked() {
        console.log('开始游戏按钮被点击了！');
        // 在这里添加切换到游戏场景的逻辑
        director.loadScene('Game'); // 假设游戏场景名为 Game，先注释掉，因为场景还没创建
    }

    update(deltaTime: number) {
        // 每帧执行的操作
    }
} 