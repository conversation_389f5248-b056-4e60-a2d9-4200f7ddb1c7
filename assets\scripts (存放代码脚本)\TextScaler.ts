import { _decorator, Component, Label, view, screen } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 文本大小自适应组件
 * 根据屏幕宽度自动调整文本大小
 */
@ccclass('TextScaler')
export class TextScaler extends Component {
    @property({ tooltip: '基准设计宽度' })
    designWidth: number = 750;

    @property({ tooltip: '最小字体大小' })
    minFontSize: number = 10;

    @property({ tooltip: '要自适应的Label组件列表', type: [Label] })
    labels: Label[] = [];

    // 存储原始字体大小
    private _originalSizes: Map<string, number> = new Map();

    start() {
        // 记录初始字体大小
        this.labels.forEach(label => {
            if (label && label.isValid) {
                this._originalSizes.set(label.uuid, label.fontSize);
            }
        });

        // 初始调整
        this.updateFontSize();

        // 监听屏幕大小变化
        view.on('canvas-resize', this.updateFontSize, this);
    }

    onDestroy() {
        // 移除监听
        if (view) {
            view.off('canvas-resize', this.updateFontSize, this);
        }
    }

    /**
     * 更新字体大小
     */
    updateFontSize() {
        // 获取当前屏幕宽度
        const screenWidth = screen.windowSize.width;
        
        // 计算缩放比例
        const scale = Math.max(screenWidth / this.designWidth, 0.5);
        
        // 应用到所有标签
        this.labels.forEach(label => {
            if (label && label.isValid) {
                const originalSize = this._originalSizes.get(label.uuid) || label.fontSize;
                // 计算新的字体大小，并确保不小于最小值
                const newSize = Math.max(Math.floor(originalSize * scale), this.minFontSize);
                label.fontSize = newSize;
            }
        });
    }

    /**
     * 添加一个Label到自适应列表
     */
    addLabel(label: Label) {
        if (label && label.isValid && this.labels.indexOf(label) === -1) {
            // 记录原始大小
            this._originalSizes.set(label.uuid, label.fontSize);
            
            // 添加到列表
            this.labels.push(label);
            
            // 立即应用缩放
            this.updateFontSize();
        }
    }
}





