import { _decorator, Component, director, Label, find } from 'cc';
import { WordManager } from './WordManager';
import { LetterGridManager } from './LetterGridManager';
const { ccclass } = _decorator;

@ccclass('GameManager')
export class GameManager extends Component {
    // 我们不再使用属性面板引用，而是在代码中查找
    private timeLabel: Label = null;
    
    private gameTime: number = 0;
    private isTimerRunning: boolean = false;
    
    start() {
        console.log('游戏场景脚本启动');
        
        // 在代码中查找计时器节点
        const timerNode = find('Canvas/顶部信息栏/计时器');
        if (timerNode) {
            this.timeLabel = timerNode.getComponent(Label);
            console.log('找到计时器标签');
        } else {
            console.error('未找到计时器节点');
        }
        
        // 初始化时间显示
        this.resetTimer();
        // 开始计时
        this.startTimer();
    }

    /**
     * 重置计时器
     */
    resetTimer() {
        this.gameTime = 0;
        this.updateTimeDisplay();
    }
    
    /**
     * 开始计时
     */
    startTimer() {
        this.isTimerRunning = true;
    }
    
    /**
     * 暂停计时
     */
    pauseTimer() {
        this.isTimerRunning = false;
    }
    
    /**
     * 更新时间显示
     */
    updateTimeDisplay() {
        if (this.timeLabel) {
            this.timeLabel.string = `时间：${Math.floor(this.gameTime)}`;
        }
    }

    /**
     * 当返回按钮被点击时调用
     */
    onBackButtonClicked() {
        console.log('返回按钮被点击！');
        // 停止计时
        this.pauseTimer();

        // 在加载新场景前，手动触发当前场景中所有组件的 onDestroy
        // 这可以确保事件监听器被正确清理
        const scene = director.getScene();
        if (scene) {
            // 延迟一帧加载新场景，确保当前帧的清理工作完成
            this.scheduleOnce(() => {
                director.loadScene('StartMenu');
            }, 0);
        } else {
            // 直接加载主菜单场景
            director.loadScene('StartMenu');
        }
    }

    /**
     * 重新生成游戏（重新生成字母网格和单词）
     */
    onRegenerateButtonClicked() {
        console.log('重新生成游戏！');

        // 重置计时器
        this.resetTimer();
        this.startTimer();

        // 查找并调用 WordManager 的重新生成方法
        const wordManagerNode = find('Canvas/单词管理器');
        if (wordManagerNode) {
            const wordManager = wordManagerNode.getComponent('WordManager') as any;
            if (wordManager && wordManager.regenerateGame) {
                wordManager.regenerateGame();
                console.log('成功调用 WordManager 重新生成');
            } else {
                console.warn('WordManager 组件或 regenerateGame 方法不存在');
            }
        } else {
            console.warn('未找到单词管理器节点');
        }

        // 查找并调用 LetterGridManager 的重新生成方法
        const letterGridNode = find('Canvas/字母区域');
        if (letterGridNode) {
            const letterGridManager = letterGridNode.getComponent('LetterGridManager') as any;
            if (letterGridManager && letterGridManager.regenerateGrid) {
                // 延迟调用，确保 WordManager 先完成
                this.scheduleOnce(() => {
                    letterGridManager.regenerateGrid();
                    console.log('成功调用 LetterGridManager 重新生成');
                }, 0.1);
            } else {
                console.warn('LetterGridManager 组件或 regenerateGrid 方法不存在');
            }
        } else {
            console.warn('未找到字母区域节点');
        }
    }

    update(deltaTime: number) {
        // 如果计时器正在运行，更新时间
        if (this.isTimerRunning) {
            this.gameTime += deltaTime;
            this.updateTimeDisplay();
        }
    }
} 
