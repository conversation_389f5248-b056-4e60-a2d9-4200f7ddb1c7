{"__version__": "1.3.9", "bundleConfig": {"custom": {"default": {"displayName": "i18n:builder.asset_bundle.defaultConfig", "configs": {"native": {"preferredOptions": {"isRemote": false, "compressionType": "merge_dep"}, "overwriteSettings": {"ios": {"isRemote": false, "compressionType": "merge_dep"}, "android": {"isRemote": false, "compressionType": "merge_dep"}, "windows": {"isRemote": false, "compressionType": "merge_dep"}}}, "web": {"preferredOptions": {"isRemote": false, "compressionType": "merge_dep"}, "fallbackOptions": {"compressionType": "merge_dep"}}, "miniGame": {"fallbackOptions": {"isRemote": false, "compressionType": "merge_dep"}, "configMode": "overwrite", "overwriteSettings": {"alipay-mini-game": {"compressionType": "subpackage"}, "bytedance-mini-game": {"compressionType": "subpackage"}, "honor-mini-game": {"compressionType": "subpackage"}, "huawei-quick-game": {"compressionType": "subpackage"}, "oppo-mini-game": {"compressionType": "subpackage"}, "migu-mini-game": {"compressionType": "subpackage"}, "taobao-mini-game": {"compressionType": "subpackage"}, "vivo-mini-game": {"compressionType": "subpackage"}, "wechatgame": {"compressionType": "subpackage"}, "xiaomi-quick-game": {"compressionType": "subpackage"}}}}}}}}